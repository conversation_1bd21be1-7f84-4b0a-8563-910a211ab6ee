"""
飞书流式处理器模块
负责处理Agent的流式响应并更新卡片
"""
import time
import asyncio
from agents import Runner
# 不再需要导入 model_provider，因为模型直接在 Agent 创建时指定
from src.services.agent.bots.base_bot import BaseBot
from src.services.feishu.agent_message_formatter import extract_content_from_stream_event
from src.services.agent.runner import _extract_agent_name_from_tool_output
from src.utils.logger import logger
from .config import FeishuConfig
from .card_service import CardService
from .agent_service import AgentService


class StreamProcessor:
    """流式响应处理器"""
    
    def __init__(self):
        self.timeout_minutes = FeishuConfig.get_stream_timeout_minutes()
        self.message_steps = FeishuConfig.get_message_steps()
        self.max_retries, self.retry_interval = FeishuConfig.get_retry_config()
    
    @staticmethod
    async def process_agent_stream(
        bot:BaseBot,
        user_query,
        user_info_obj,
        card_id,
        initial_sequence: int,
        history=None,
        image_url: str | None = None,
    ) -> tuple[int, str, str, dict, bool | None, list]:
        """处理Agent的流式响应并更新卡片"""
        processor = StreamProcessor()
        return await processor._process_with_retry(
            bot, user_query, user_info_obj, card_id, initial_sequence, history, image_url
        )
    
    async def _process_with_retry(
        self, bot:BaseBot, user_query, user_info_obj, card_id, initial_sequence, history, image_url
    ) -> tuple[int, str, str, dict, bool | None, list]:
        """带重试的流式处理"""
        agent = bot.create_agent()
        input_messages = AgentService.prepare_input_messages(history, user_query, image_url)
        input_messages = bot.get_user_realtime_instruction_as_message_object() + input_messages
        start_time = time.time()
        
        for retry_count in range(self.max_retries + 1):
            logger.info(f"开始流式处理，重试次数: {retry_count}")
            
            try:
                result = await self._run_single_stream(
                    agent, input_messages, user_info_obj, card_id, initial_sequence, start_time
                )
                
                sequence, response, log_message, structured_msg, timeouted, used_agents = result
                
                # 检查是否需要重试
                if self._should_retry(log_message, retry_count):
                    logger.warning(f"未检测到有效的工具调用，开始第{retry_count + 1}次重试")
                    sequence = await CardService.handle_retry_logic(
                        card_id, sequence, retry_count + 1, self.retry_interval
                    )
                    initial_sequence = sequence  # 更新序列号为下次重试使用
                    continue
                
                # 成功完成
                return result
                
            except Exception as e:
                logger.error(f"流式处理出错 (重试{retry_count}): {e}")
                if retry_count == self.max_retries:
                    raise
                await asyncio.sleep(self.retry_interval)
        
        # 不应该到达这里
        raise Exception("流式处理重试次数已达上限")
    
    def _should_retry(self, log_message: str, retry_count: int) -> bool:
        """判断是否需要重试（Agent as Tool架构）"""
        # 检测工具调用成功的标志
        has_tool_call = (
            "CoordinatorBot调用专业工具" in log_message or
            "专业工具" in log_message or
            "analysis" in log_message.lower() or
            len(log_message.strip()) > 50  # 有实质性输出
        )
        can_retry = retry_count < self.max_retries

        if has_tool_call:
            logger.info("检测到有效的工具调用或输出，流式处理成功")
        elif not can_retry:
            logger.warning("达到最大重试次数，停止重试")

        return not has_tool_call and can_retry
    
    async def _run_single_stream(
        self, agent, input_messages, user_info_obj, card_id, initial_sequence, start_time
    ) -> tuple[int, str, str, dict, bool, list]:
        """执行单次流式处理"""
        # 初始化状态
        state = StreamState(initial_sequence)
        
        # 运行Agent
        result = Runner.run_streamed(
            agent,
            max_turns=20,
            input=input_messages,
            context=user_info_obj,
        )
        
        # 处理流事件
        await self._process_stream_events(result, state, card_id, start_time)
        
        # 发送最终响应
        await self._send_final_response(state, card_id)
        
        # 提取结构化消息
        structured_message = AgentService.extract_structured_assistant_message(result)
        
        return (
            state.current_sequence,
            state.full_response,
            state.full_log_message,
            structured_message,
            state.timeouted,
            state.used_agents,
        )
    
    async def _process_stream_events(self, result, state: 'StreamState', card_id: str, start_time: float):
        """处理流事件"""
        stream_events = result.stream_events()
        
        try:
            # 等待第一个事件，设置超时600秒
            first_event = await asyncio.wait_for(stream_events.__anext__(), timeout=600)
            await self._handle_single_event(first_event, state, card_id, start_time)
            
            # 处理后续事件
            async for event in stream_events:
                if self._check_timeout(start_time):
                    logger.warning("流式处理超时，停止处理")
                    state.timeouted = True
                    break
                
                await self._handle_single_event(event, state, card_id, start_time)
                
        except asyncio.TimeoutError:
            logger.error("等待第一个流事件超时")
            raise Exception("流式处理启动超时，请检查模型API连接")
        except StopAsyncIteration:
            logger.info("流事件处理完成")
        except Exception as e:
            state.full_log_message += f"处理流事件时出错: {str(e)}"
            logger.error(f"处理流事件时出错: {e}")
            raise
    
    async def _handle_single_event(self, event, state: 'StreamState', card_id: str, start_time: float):
        """处理单个流事件"""
        state.event_count += 1
        
        # 记录进度
        if state.event_count % 10 == 0:
            logger.info(f"已处理 {state.event_count} 个事件，类型: {event.type}")
        
        # 提取内容
        content_chunk, log_message = extract_content_from_stream_event(event)

        if log_message:
            state.full_log_message += log_message

            # 检测工具输出并提取agent名称
            if "工具输出:" in log_message:
                # 提取工具输出内容
                tool_output = log_message.replace("工具输出:", "").strip()
                if tool_output and not tool_output.startswith("AI读取了DDL文件"):
                    agent_name = _extract_agent_name_from_tool_output(tool_output)
                    if agent_name and agent_name not in state.used_agents:
                        state.used_agents.append(agent_name)
                        logger.info(f"🔧 StreamProcessor检测到agent: {agent_name}")
        
        if content_chunk:
            state.add_content(content_chunk)
            
            # 检查是否需要发送更新
            if await self._should_send_update(state, start_time):
                state.current_sequence = await CardService.send_thinking_and_reply_updates(
                    card_id, state.full_response, state.current_sequence
                )
                state.reset_accumulation()
    
    async def _should_send_update(self, state: 'StreamState', start_time: float) -> bool:
        """检查是否应该发送更新"""
        if len(state.accumulated_content) <= self.message_steps:
            return False
        
        # 检查超时
        if self._check_timeout(start_time):
            logger.warning("流式响应超时，停止发送更新")
            state.timeouted = True
            return False
        
        return True
    
    def _check_timeout(self, start_time: float) -> bool:
        """检查是否超时"""
        elapsed_minutes = (time.time() - start_time) / 60
        return elapsed_minutes >= self.timeout_minutes
    
    async def _send_final_response(self, state: 'StreamState', card_id: str):
        """发送最终响应"""
        if state.current_sequence == 0:
            logger.warning("流式响应未发送任何内容")
            return
        
        if not state.has_unsent_content():
            return
        
        logger.info(f"发送最终响应到卡片 {card_id} (总长度: {len(state.full_response)})")
        state.current_sequence = await CardService.send_thinking_and_reply_updates(
            card_id, state.full_response, state.current_sequence, force_send_thinking=True
        )


class StreamState:
    """流式处理状态管理"""

    def __init__(self, initial_sequence: int):
        self.current_sequence = initial_sequence
        self.full_response = ""
        self.full_log_message = ""
        self.accumulated_content = ""
        self.last_sent_length = 0
        self.event_count = 0
        self.timeouted = False
        self.used_agents = []  # 收集使用的agent名称
    
    def add_content(self, content: str):
        """添加内容（只有 raw_response_event 的内容会被添加）"""
        if not content or not content.strip():
            return

        self.full_response += content
        self.accumulated_content += content
    
    def reset_accumulation(self):
        """重置累积状态"""
        self.last_sent_length = len(self.full_response)
        self.accumulated_content = ""
    
    def has_unsent_content(self) -> bool:
        """检查是否有未发送的内容"""
        return len(self.full_response) > self.last_sent_length