"""
对话相关的通用工具函数
"""
from typing import Optional
from src.utils.logger import logger


def get_all_user_messages_content(conversation_id: str) -> Optional[str]:
    """
    获取对话中所有用户消息内容，并格式化为 [HH:MM] 用户：内容
    
    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[str]: 所有用户消息内容拼接后的字符串，若无用户消息则返回None
    """
    try:
        from src.repositories.chatbi.history import load_conversation
        import datetime

        messages = load_conversation(conversation_id)

        if not messages:
            logger.warning(f"对话 {conversation_id} 没有消息历史")
            return None

        # 收集所有role=user的消息内容和时间
        user_msg_lines = []
        for message in messages:
            if message.get('role') == 'user':
                content = message.get('content', '').strip()
                ts = message.get('timestamp')
                # 转换时间戳为HH:MM格式
                if ts and isinstance(ts, int):
                    try:
                        dt_str = datetime.datetime.fromtimestamp(ts / 1000).strftime('%H:%M')
                    except Exception:
                        dt_str = "--:--"
                else:
                    dt_str = "--:--"
                if content:
                    # 格式：[HH:MM] 用户：内容
                    user_msg_lines.append(f"[{dt_str}] 用户：{content}")

        if user_msg_lines:
            logger.info(f"获取到所有用户消息内容，共{len(user_msg_lines)}条，总长度: {sum(len(c) for c in user_msg_lines)}")
            # 多条消息用换行拼接
            return "\n".join(user_msg_lines)
        else:
            logger.warning(f"对话 {conversation_id} 中未找到用户消息")
            return None

    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 所有用户消息时发生异常: {str(e)}", exc_info=True)
        return None


def get_last_assistant_message_content(conversation_id: str) -> Optional[str]:
    """
    获取对话中最后一条assistant回复的内容

    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[str]: 最后一条assistant回复的内容，若无assistant回复则返回None
    """
    try:
        from src.repositories.chatbi.history import load_conversation
        import datetime

        messages = load_conversation(conversation_id)

        if not messages:
            logger.warning(f"对话 {conversation_id} 没有消息历史")
            return None

        # 从后往前查找最后一条role=assistant的消息
        for message in reversed(messages):
            if message.get('role') == 'assistant':
                content = message.get('content', '').strip()
                ts = message.get('timestamp')

                # 转换时间戳为HH:MM格式
                if ts and isinstance(ts, int):
                    try:
                        dt_str = datetime.datetime.fromtimestamp(ts / 1000).strftime('%H:%M')
                    except Exception:
                        dt_str = "--:--"
                else:
                    dt_str = "--:--"

                if content:
                    # 格式：[HH:MM] 助手：内容
                    formatted_content = f"[{dt_str}] 助手：{content}"
                    logger.info(f"获取到最后一条assistant回复，长度: {len(formatted_content)}")
                    return formatted_content

        logger.warning(f"对话 {conversation_id} 中未找到assistant回复")
        return None

    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 最后一条assistant回复时发生异常: {str(e)}", exc_info=True)
        return None
