# Bad Case 通知功能增强示例

## 修改概述

在发送 bad case 消息时，现在会同时包含 conversation 的最后一条 assistant 回复内容，以便更好地了解问题的上下文。

## 修改内容

### 1. 新增函数：获取最后一条 assistant 回复

**文件**: `src/utils/conversation_utils.py`

```python
def get_last_assistant_message_content(conversation_id: str) -> Optional[str]:
    """
    获取对话中最后一条assistant回复的内容
    
    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[str]: 最后一条assistant回复的内容，若无assistant回复则返回None
    """
```

### 2. 更新通知发送逻辑

**文件**: `src/services/chatbot/bad_case_service.py`

在 `_send_notification_async` 函数中：

```python
# 获取最后一条assistant回复内容
last_assistant_content = get_last_assistant_message_content(conversation_id)

# 传递最后一条assistant回复到通知函数
notification_sent = notification_func(
    conversation_id,
    user_name,  # 标记用户
    all_user_messages_content,
    conversation_user_name,  # 对话用户
    last_assistant_content  # 最后一条assistant回复
)
```

### 3. 更新通知函数签名

**文件**: `src/services/feishu/notification_sender.py`

所有相关的通知函数都添加了 `last_assistant_message` 参数：

```python
def send_bad_case_notification(
    conversation_id: str, 
    user_name: str = None, 
    user_message: str = None, 
    conversation_user_name: str = None, 
    last_assistant_message: str = None  # 新增参数
) -> bool:
```

### 4. 更新通知消息内容

在通知消息中添加了 assistant 回复部分：

```python
def _build_assistant_message_section(assistant_message: str) -> str:
    """构建assistant回复内容部分"""
    if not assistant_message:
        return ""
    
    truncated_message = _truncate_message(assistant_message, max_length=300)
    return f"\n**最后一条助手回复**:\n{truncated_message}\n"
```

## 通知消息示例

修改后的 bad case 通知消息格式：

```
🚨 Bad Case 标记通知

**标记用户**: 张三
**对话用户**: 李四
**对话ID**: `conv_123456789`
**标记时间**: 2025-07-12 14:30:15

**用户消息内容**:
[14:28] 用户：请帮我查询销售数据
[14:29] 用户：为什么查询结果不对？

**最后一条助手回复**:
[14:30] 助手：抱歉，我在查询销售数据时遇到了问题。数据库连接超时，请稍后重试...

**操作**: [📊 查看对话详情](https://chat-bi.summerfarm.net/dashboard?chat=conv_123456789)

---
请相关人员及时关注和处理。
```

## 兼容性

- ✅ 向后兼容：所有新参数都是可选的，现有代码无需修改
- ✅ 统一性：Bad Case 和 Good Case 通知都支持相同的功能
- ✅ 错误处理：如果获取 assistant 回复失败，不会影响通知发送

## 使用方法

无需修改现有调用代码，功能会自动生效：

```python
# 现有代码无需修改，会自动包含assistant回复
mark_bad_case("conversation_123", is_bad_case=True, user_name="张三")
```

## 测试验证

所有修改都已通过测试验证：
- ✅ 新函数正确实现
- ✅ 通知函数签名正确更新
- ✅ 消息内容正确包含assistant回复
- ✅ 向后兼容性保持
